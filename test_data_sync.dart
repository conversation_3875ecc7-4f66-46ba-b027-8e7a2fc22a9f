import 'dart:convert';
import 'dart:io';

// 测试数据同步功能
void main() async {
  print('🔄 测试数据同步功能...');
  print('=' * 60);
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  // 模拟用户信息
  final userId = 'user_1752759707518_e2f72wq7j'; // 之前注册的用户ID
  final token = 'test-token-123';
  
  try {
    // 1. 测试上传数据同步
    print('📤 测试数据同步上传...');
    await testSyncUpload(client, baseUrl, userId, token);
    
    // 2. 测试下载数据同步
    print('\n📥 测试数据同步下载...');
    await testSyncDownload(client, baseUrl, userId, token);
    
    // 3. 测试同步状态查询
    print('\n📊 测试同步状态查询...');
    await testSyncStatus(client, baseUrl, userId, token);
    
    print('\n' + '=' * 60);
    print('🎉 数据同步功能测试完成！');
    
  } catch (e) {
    print('❌ 测试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> testSyncUpload(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/upload'));
    request.headers.contentType = ContentType.json;
    
    final syncData = {
      'userId': userId,
      'token': token,
      'syncData': {
        'userSettings': {
          'enableBiometric': true,
          'autoSync': true,
          'enableNotification': false,
          'theme': 'dark',
          'language': 'zh-CN'
        },
        'userFavorites': [
          {
            'id': 'fav_001',
            'type': 'novel',
            'title': '测试小说1',
            'author': '测试作者1',
            'addedAt': DateTime.now().toIso8601String()
          },
          {
            'id': 'fav_002',
            'type': 'character',
            'name': '测试角色1',
            'description': '这是一个测试角色',
            'addedAt': DateTime.now().toIso8601String()
          }
        ],
        'userHistory': [
          {
            'id': 'hist_001',
            'type': 'novel_read',
            'novelId': 'novel_001',
            'title': '阅读历史1',
            'lastReadAt': DateTime.now().toIso8601String(),
            'progress': 0.5
          },
          {
            'id': 'hist_002',
            'type': 'character_created',
            'characterId': 'char_001',
            'name': '创建角色历史',
            'createdAt': DateTime.now().toIso8601String()
          }
        ]
      }
    };
    
    request.add(utf8.encode(jsonEncode(syncData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        print('   ✅ 数据上传成功');
        print('   同步时间: ${result['data']['syncedAt']}');
        print('   同步类型: ${result['data']['syncedTypes'].join(', ')}');
        
        final results = result['data']['results'];
        if (results['userSettings'] != null) {
          print('   用户设置: ${results['userSettings']['action']} (${results['userSettings']['count']}条)');
        }
        if (results['userFavorites'] != null) {
          print('   用户收藏: ${results['userFavorites']['action']} (${results['userFavorites']['count']}条)');
        }
        if (results['userHistory'] != null) {
          print('   用户历史: ${results['userHistory']['action']} (${results['userHistory']['count']}条)');
        }
      } else {
        print('   ❌ 上传失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}

Future<void> testSyncDownload(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/download'));
    request.headers.contentType = ContentType.json;
    
    final requestData = {
      'userId': userId,
      'token': token,
      'dataTypes': ['userSettings', 'userFavorites', 'userHistory']
    };
    
    request.add(utf8.encode(jsonEncode(requestData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        print('   ✅ 数据下载成功');
        print('   同步时间: ${result['data']['syncedAt']}');
        print('   可用类型: ${result['data']['availableTypes'].join(', ')}');
        
        final syncData = result['data']['syncData'];
        if (syncData['userSettings'] != null) {
          print('   用户设置: 主题=${syncData['userSettings']['theme']}, 语言=${syncData['userSettings']['language']}');
        }
        if (syncData['userFavorites'] != null) {
          print('   用户收藏: ${syncData['userFavorites'].length}条');
        }
        if (syncData['userHistory'] != null) {
          print('   用户历史: ${syncData['userHistory'].length}条');
        }
      } else {
        print('   ❌ 下载失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}

Future<void> testSyncStatus(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/status'));
    request.headers.contentType = ContentType.json;
    
    final requestData = {
      'userId': userId,
      'token': token
    };
    
    request.add(utf8.encode(jsonEncode(requestData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        final status = result['data'];
        print('   ✅ 状态查询成功');
        print('   用户ID: ${status['userId']}');
        print('   最后同步: ${status['lastSyncTime'] ?? '从未同步'}');
        print('   同步状态: ${status['isEnabled'] ? '已启用' : '已禁用'}');
        
        final dataTypes = status['dataTypes'];
        print('   数据详情:');
        dataTypes.forEach((type, info) {
          print('     $type: ${info['count']}条, 最后更新: ${info['lastUpdated'] ?? '无'}');
        });
      } else {
        print('   ❌ 状态查询失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}
