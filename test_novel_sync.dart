import 'dart:convert';
import 'dart:io';

// 测试小说数据同步功能
void main() async {
  print('📚 测试小说数据同步功能...');
  print('=' * 60);
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  // 模拟用户信息
  final userId = 'user_1752759707518_e2f72wq7j'; // 之前注册的用户ID
  final token = 'test-token-123';
  
  try {
    // 1. 测试上传小说数据
    print('📤 测试小说数据上传...');
    await testNovelSyncUpload(client, baseUrl, userId, token);
    
    // 2. 测试下载小说数据
    print('\n📥 测试小说数据下载...');
    await testNovelSyncDownload(client, baseUrl, userId, token);
    
    // 3. 测试小说同步状态
    print('\n📊 测试小说同步状态...');
    await testNovelSyncStatus(client, baseUrl, userId, token);
    
    print('\n' + '=' * 60);
    print('🎉 小说数据同步功能测试完成！');
    
  } catch (e) {
    print('❌ 测试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> testNovelSyncUpload(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/upload'));
    request.headers.contentType = ContentType.json;
    
    final syncData = {
      'userId': userId,
      'token': token,
      'syncData': {
        // 长篇小说数据
        'novels': [
          {
            'id': 'novel_001',
            'title': '测试长篇小说1',
            'author': '测试作者',
            'description': '这是一个测试的长篇小说描述',
            'genre': '奇幻',
            'status': 'writing',
            'wordCount': 50000,
            'chapterCount': 10,
            'createdAt': DateTime.now().toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'tags': ['奇幻', '冒险', '魔法'],
            'outline': '这是小说的大纲内容...',
            'settings': {
              'worldBuilding': '世界观设定...',
              'characterSettings': '角色设定...'
            }
          },
          {
            'id': 'novel_002',
            'title': '测试长篇小说2',
            'author': '测试作者',
            'description': '这是第二个测试长篇小说',
            'genre': '科幻',
            'status': 'completed',
            'wordCount': 120000,
            'chapterCount': 25,
            'createdAt': DateTime.now().subtract(Duration(days: 30)).toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'tags': ['科幻', '未来', '机器人'],
            'outline': '科幻小说的大纲...',
            'settings': {
              'worldBuilding': '未来世界设定...',
              'technologySettings': '科技设定...'
            }
          }
        ],
        
        // 短篇小说数据
        'shortStories': [
          {
            'id': 'story_001',
            'title': '测试短篇故事1',
            'author': '测试作者',
            'content': '这是一个完整的短篇故事内容。故事讲述了一个勇敢的少年...',
            'genre': '冒险',
            'wordCount': 3000,
            'status': 'completed',
            'createdAt': DateTime.now().subtract(Duration(days: 7)).toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'tags': ['冒险', '青春', '成长']
          },
          {
            'id': 'story_002',
            'title': '测试短篇故事2',
            'author': '测试作者',
            'content': '另一个短篇故事的内容。这个故事更加温馨...',
            'genre': '温馨',
            'wordCount': 2500,
            'status': 'completed',
            'createdAt': DateTime.now().subtract(Duration(days: 3)).toIso8601String(),
            'updatedAt': DateTime.now().toIso8601String(),
            'tags': ['温馨', '家庭', '亲情']
          }
        ],
        
        // 小说章节数据
        'novelChapters': [
          {
            'id': 'chapter_001_001',
            'novelId': 'novel_001',
            'chapterNumber': 1,
            'title': '第一章：开始的冒险',
            'content': '这是第一章的内容。主人公踏上了冒险的旅程...',
            'wordCount': 5000,
            'status': 'published',
            'createdAt': DateTime.now().subtract(Duration(days: 20)).toIso8601String(),
            'updatedAt': DateTime.now().subtract(Duration(days: 15)).toIso8601String()
          },
          {
            'id': 'chapter_001_002',
            'novelId': 'novel_001',
            'chapterNumber': 2,
            'title': '第二章：遇见伙伴',
            'content': '在第二章中，主人公遇到了重要的伙伴...',
            'wordCount': 4800,
            'status': 'published',
            'createdAt': DateTime.now().subtract(Duration(days: 18)).toIso8601String(),
            'updatedAt': DateTime.now().subtract(Duration(days: 12)).toIso8601String()
          },
          {
            'id': 'chapter_002_001',
            'novelId': 'novel_002',
            'chapterNumber': 1,
            'title': '第一章：未来世界',
            'content': '科幻小说的第一章，描述了未来世界的景象...',
            'wordCount': 4500,
            'status': 'published',
            'createdAt': DateTime.now().subtract(Duration(days: 25)).toIso8601String(),
            'updatedAt': DateTime.now().subtract(Duration(days: 20)).toIso8601String()
          }
        ]
      }
    };
    
    request.add(utf8.encode(jsonEncode(syncData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        print('   ✅ 小说数据上传成功');
        print('   同步时间: ${result['data']['syncedAt']}');
        print('   同步类型: ${result['data']['syncedTypes'].join(', ')}');
        
        final results = result['data']['results'];
        if (results['novels'] != null) {
          print('   长篇小说: 新增${results['novels']['added']}部, 更新${results['novels']['updated']}部');
        }
        if (results['shortStories'] != null) {
          print('   短篇小说: 新增${results['shortStories']['added']}部, 更新${results['shortStories']['updated']}部');
        }
        if (results['novelChapters'] != null) {
          print('   小说章节: 新增${results['novelChapters']['added']}章, 更新${results['novelChapters']['updated']}章');
        }
      } else {
        print('   ❌ 上传失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}

Future<void> testNovelSyncDownload(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/download'));
    request.headers.contentType = ContentType.json;
    
    final requestData = {
      'userId': userId,
      'token': token,
      'dataTypes': ['novels', 'shortStories', 'novelChapters']
    };
    
    request.add(utf8.encode(jsonEncode(requestData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        print('   ✅ 小说数据下载成功');
        print('   同步时间: ${result['data']['syncedAt']}');
        print('   可用类型: ${result['data']['availableTypes'].join(', ')}');
        
        final syncData = result['data']['syncData'];
        if (syncData['novels'] != null) {
          print('   长篇小说: ${syncData['novels'].length}部');
          for (var novel in syncData['novels']) {
            print('     - ${novel['title']} (${novel['genre']}, ${novel['wordCount']}字)');
          }
        }
        if (syncData['shortStories'] != null) {
          print('   短篇小说: ${syncData['shortStories'].length}部');
          for (var story in syncData['shortStories']) {
            print('     - ${story['title']} (${story['genre']}, ${story['wordCount']}字)');
          }
        }
        if (syncData['novelChapters'] != null) {
          print('   小说章节: ${syncData['novelChapters'].length}章');
          for (var chapter in syncData['novelChapters']) {
            print('     - ${chapter['title']} (${chapter['wordCount']}字)');
          }
        }
      } else {
        print('   ❌ 下载失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}

Future<void> testNovelSyncStatus(HttpClient client, String baseUrl, String userId, String token) async {
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/sync/status'));
    request.headers.contentType = ContentType.json;
    
    final requestData = {
      'userId': userId,
      'token': token
    };
    
    request.add(utf8.encode(jsonEncode(requestData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        final status = result['data'];
        print('   ✅ 小说同步状态查询成功');
        print('   用户ID: ${status['userId']}');
        print('   最后同步: ${status['lastSyncTime'] ?? '从未同步'}');
        print('   同步状态: ${status['isEnabled'] ? '已启用' : '已禁用'}');
        
        final dataTypes = status['dataTypes'];
        print('   小说数据详情:');
        if (dataTypes['novels'] != null) {
          print('     长篇小说: ${dataTypes['novels']['count']}部, 最后更新: ${dataTypes['novels']['lastUpdated'] ?? '无'}');
        }
        if (dataTypes['shortStories'] != null) {
          print('     短篇小说: ${dataTypes['shortStories']['count']}部, 最后更新: ${dataTypes['shortStories']['lastUpdated'] ?? '无'}');
        }
        if (dataTypes['novelChapters'] != null) {
          print('     小说章节: ${dataTypes['novelChapters']['count']}章, 最后更新: ${dataTypes['novelChapters']['lastUpdated'] ?? '无'}');
        }
      } else {
        print('   ❌ 状态查询失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}
