import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

// 测试用户注册API
void main() async {
  print('👤 测试用户注册API...');
  print('=' * 50);
  
  final client = HttpClient();
  final baseUrl = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';
  
  try {
    // 测试注册新用户
    await testRegister(client, baseUrl, {
      'username': 'newuser_${DateTime.now().millisecondsSinceEpoch}',
      'password': _hashPassword('test123'),
      'phoneNumber': '13900${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}',
      'verificationCode': '123456',
      'memberCode': null
    });
    
    // 测试使用会员码注册
    await testRegister(client, baseUrl, {
      'username': 'vipuser_${DateTime.now().millisecondsSinceEpoch}',
      'password': _hashPassword('test123'),
      'phoneNumber': '13800${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}',
      'verificationCode': '123456',
      'memberCode': 'VIP2024001'
    });
    
    // 测试重复用户名
    await testRegister(client, baseUrl, {
      'username': 'testuser', // 已存在的用户名
      'password': _hashPassword('test123'),
      'phoneNumber': '13700${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}',
      'verificationCode': '123456',
      'memberCode': null
    });
    
    // 测试重复手机号
    await testRegister(client, baseUrl, {
      'username': 'anotheruser_${DateTime.now().millisecondsSinceEpoch}',
      'password': _hashPassword('test123'),
      'phoneNumber': '13800138000', // 已存在的手机号
      'verificationCode': '123456',
      'memberCode': null
    });
    
  } catch (e) {
    print('❌ 测试失败: $e');
  } finally {
    client.close();
  }
}

Future<void> testRegister(HttpClient client, String baseUrl, Map<String, dynamic> userData) async {
  print('\n📝 测试注册用户');
  print('   用户名: ${userData['username']}');
  print('   手机号: ${userData['phoneNumber']}');
  print('   会员码: ${userData['memberCode'] ?? '无'}');
  
  try {
    final request = await client.postUrl(Uri.parse('$baseUrl/auth/register'));
    request.headers.contentType = ContentType.json;
    
    request.add(utf8.encode(jsonEncode(userData)));
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    if (response.statusCode == 200) {
      final result = jsonDecode(responseBody);
      if (result['success'] == true) {
        print('   ✅ 注册成功');
        print('   用户ID: ${result['data']['user']['id']}');
        print('   用户名: ${result['data']['user']['username']}');
        print('   手机号: ${result['data']['user']['phoneNumber']}');
        print('   会员状态: ${result['data']['user']['isMember'] ? "是会员" : "非会员"}');
        print('   会员类型: ${result['data']['user']['membershipType']}');
        print('   Token: ${result['data']['token'].substring(0, 20)}...');
      } else {
        print('   ❌ 注册失败: ${result['message']}');
      }
    } else {
      print('   ❌ HTTP错误: ${response.statusCode}');
      final result = jsonDecode(responseBody);
      print('   错误信息: ${result['message']}');
    }
  } catch (e) {
    print('   ❌ 异常: $e');
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
